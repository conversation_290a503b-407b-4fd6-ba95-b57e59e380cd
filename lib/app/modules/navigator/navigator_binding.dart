import 'package:get/get.dart';
import 'package:shadfdpcardgame/app/modules/game/game_controller.dart';
import 'package:shadfdpcardgame/app/modules/tables/tables_controller.dart';

import 'navigator_controller.dart';

class NavigatorBinding extends Bindings {
  @override
  void dependencies() {
    Get.put<NavigatorController>(NavigatorController());
    Get.lazyPut<TablesController>(() => TablesController());
    Get.lazyPut<GameController>(() => GameController());
  }
}
