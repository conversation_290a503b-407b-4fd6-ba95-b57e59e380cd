import 'package:shadfdpcardgame/app/modules/game/game_view.dart';
import 'package:shadfdpcardgame/app/modules/tables/tables_view.dart';
import 'package:shadfdpcardgame/app/modules/settings/settings_controller.dart';
import 'package:shadfdpcardgame/app/modules/settings/settings_view.dart';
import 'package:get/get.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';
import 'navigator_controller.dart';

final SettingsController settingsController = Get.find<SettingsController>();

class NavigatorView extends GetView<NavigatorController> {
  const NavigatorView({super.key});
  @override
  Widget build(BuildContext context) {
    NavigationItem buildButton(String label, IconData icon) {
      return NavigationItem(label: Text(label), child: Icon(icon));
    }

    return Scaffold(
      footers: [
        const Divider(),
        Obx(
          () => NavigationBar(
            alignment: NavigationBarAlignment.spaceAround,
            labelType: NavigationLabelType.none,
            expanded: false,
            expands: false,
            onSelected: (index) {
              controller.selectedIndex.value = index;
            },
            index: controller.selectedIndex.value,
            children: [
              // buildButton('Home', BootstrapIcons.house),
              buildButton('Tables', BootstrapIcons.cardList),
              buildButton('Game', BootstrapIcons.play),
              buildButton('Profile', BootstrapIcons.person),
            ],
          ),
        ),
      ],
      child: Obx(() {
        return IndexedStack(
          index: controller.selectedIndex.value,
          children: [
            // ShadcnApp(
            //   theme: ThemeData(
            //     colorScheme: settingsController.shadcnTheme(),
            //     radius: 0.5,
            //   ),
            //   home: Center(child: Text('Home')),
            // ),
            ShadcnApp(
              theme: ThemeData(
                colorScheme: settingsController.shadcnTheme(),
                radius: 0.5,
              ),
              home: TablesView(),
            ),
            ShadcnApp(
              theme: ThemeData(
                colorScheme: settingsController.shadcnTheme(),
                radius: 0.5,
              ),
              home: GameView(),
            ),
            ShadcnApp(
              theme: ThemeData(
                colorScheme: settingsController.shadcnTheme(),
                radius: 0.5,
              ),
              home: SettingsView(),
            ),
          ],
        );
      }),
    );
  }
}
