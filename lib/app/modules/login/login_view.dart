import 'package:shadfdpcardgame/app/utils/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart' as shadcn;

import 'package:get/get.dart';

import 'login_controller.dart';

import '../settings/settings_controller.dart';

final SettingsController settingsController = Get.find<SettingsController>();

class LoginView extends GetView<LoginController> {
  const LoginView({super.key});
  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: shadcn.ShadcnApp(
        theme: shadcn.ThemeData(
          colorScheme: settingsController.shadcnTheme(),
          radius: 0.5,
        ),
        home: shadcn.Scaffold(
          child: Center(
            child: Stack(
              children: [
                Container(
                  height: size.height * 0.70,
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage('images/logo.webp'),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.bottomCenter,
                  child: Container(
                    height: size.height * 0.40,
                    decoration: BoxDecoration(
                      color: shadcn.Theme.of(context).colorScheme.background,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                      ),
                    ),

                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(24.0),
                        child: SingleChildScrollView(
                          child: ConstrainedBox(
                            constraints: BoxConstraints(maxWidth: 750),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                shadcn.TextField(
                                  controller: controller.emailController,
                                  onChanged:
                                      (value) =>
                                          controller.validateEmail(value),

                                  placeholder: Text('Enter your email'),
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Obx(
                                      () => CustomErrorText(
                                        text: controller.emailError.value,
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 5),
                                shadcn.TextField(
                                  controller: controller.passwordController,
                                  onChanged:
                                      (value) =>
                                          controller.validatePassword(value),
                                  placeholder: Text('Enter your password'),
                                  features: [
                                    shadcn.InputFeature.passwordToggle(
                                      mode: shadcn.PasswordPeekMode.toggle,
                                    ),
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Obx(
                                      () => CustomErrorText(
                                        text: controller.passwordError.value,
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 10),
                                Obx(
                                  () => Row(
                                    children: [
                                      Expanded(
                                        child:
                                            controller.isLoading.value
                                                ? Column(
                                                  children: [
                                                    shadcn.CircularProgressIndicator(),
                                                  ],
                                                )
                                                : shadcn.PrimaryButton(
                                                  onPressed: () {
                                                    controller.getLogin();
                                                  },
                                                  child: Text('Login'),
                                                ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(height: 10),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    CustomText(text: "Don't have an account?"),
                                    SizedBox(width: 5),
                                    TextButton(
                                      onPressed: () {
                                        Get.toNamed('/register');
                                      },
                                      child: Text('Register...'),
                                    ),
                                  ],
                                ),
                                // SizedBox(height: 150),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
