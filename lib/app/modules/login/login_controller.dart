import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadfdpcardgame/app/data/appwrite/appwrite_service.dart';
import 'package:shadfdpcardgame/app/utils/custom_snack_bar.dart';

class LoginController extends GetxController {
  final appwriteService = Get.find<AppwriteService>();

  @override
  void onInit() {
    super.onInit();
    appwriteService.isAuthenticated();
  }

  var name = ''.obs;
  var email = ''.obs;
  var password = ''.obs;
  var nameError = ''.obs;
  var emailError = RxnString();
  var passwordError = RxnString();
  var isLoading = false.obs;

  final emailController = TextEditingController();
  final passwordController = TextEditingController();

  String? validateEmail(String value) {
    email.value = '';
    if (emailController.text == "") {
      emailError.value = null;
      return null;
    }
    if (!GetUtils.isEmail(value)) {
      emailError.value = "Please enter a valid email";
    } else {
      email.value = emailController.text;
      emailError.value = null;
    }
    return null;
  }

  String? validatePassword(String value) {
    password.value = '';
    if (passwordController.text == "") {
      passwordError.value = null;
      return null;
    }
    if (value.length < 8) {
      passwordError.value = "Password must be at least 8 characters";
    } else if (!value.contains(RegExp(r'[A-Z]'))) {
      passwordError.value = "Password must contain at least one capital letter";
    } else if (!value.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      passwordError.value = "Password must contain at least one symbol";
    } else {
      password.value = passwordController.text;
      passwordError.value = null;
    }
    return null;
  }

  Future<void> getLogin() async {
    if (email.value == "" || password.value == "") {
      customSnackBar(
        "Invalid Credentials!",
        "Please check your credentials.",
        "error",
      );
      return;
    }

    if (emailError.value != null || passwordError.value != null) {
      customSnackBar(
        "Invalid Credentials!",
        "Please check your credentials.",
        "error",
      );
      return;
    }
    // Add seconds delay
    isLoading.value = true;
    await Future.delayed(const Duration(seconds: 2));
    await appwriteService.signIn(email.value, password.value);

    // Check if the user is authenticated
    if (appwriteService.getSession.value.id != null) {
      Get.log("User is authenticated.");
      isLoading.value = false; // Ensure isLoading is reset here
    } else {
      Get.log("Authentication failed.");
      isLoading.value = false; // Ensure isLoading is reset here
    }
    isLoading.value = false; // Reset isLoading after login process
  }
}
