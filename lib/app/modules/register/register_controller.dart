import 'package:get/get.dart';
import 'package:shadfdpcardgame/app/data/appwrite/appwrite_service.dart';
import 'package:shadfdpcardgame/app/utils/custom_snack_bar.dart';

class RegisterController extends GetxController {
  final appwriteService = Get.find<AppwriteService>();

  @override
  void onInit() {
    super.onInit();
    appwriteService.isAuthenticated();
  }

  var name = ''.obs;
  var email = ''.obs;
  var password = ''.obs;
  var nameError = ''.obs;
  var emailError = RxnString();
  var passwordError = RxnString();
  var isLoading = false.obs;

  void validateName(String value) {
    name.value = "";
    if (value.length < 3 && value.isNotEmpty) {
      nameError.value = "Name must be at least 3 characters";
    } else if (value.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      nameError.value = "Name must NOT contain any symbol";
    } else {
      name.value = value;
      nameError.value = "";
    }
  }

  String? validateEmail(String value) {
    email.value = "";
    if (value == "") {
      emailError.value = null;
      return null;
    }
    if (!GetUtils.isEmail(value)) {
      emailError.value = "Please enter a valid email";
    } else {
      email.value = value;
      emailError.value = null;
    }
    return null;
  }

  String? validatePassword(String value) {
    password.value = "";
    if (value == "") {
      passwordError.value = null;
      return null;
    }
    if (value.length < 8) {
      passwordError.value = "Password must be at least 8 characters";
    } else if (!value.contains(RegExp(r'[A-Z]'))) {
      passwordError.value = "Password must contain at least one capital letter";
    } else if (!value.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      passwordError.value = "Password must contain at least one symbol";
    } else {
      password.value = value;
      passwordError.value = null;
    }
    return null;
  }

  Future<void> getRegister() async {
    Get.log(email.value);
    Get.log(password.value);
    Get.log(name.value);
    if (email.value == "" || password.value == "") {
      customSnackBar(
        "Invalid Credentials!",
        "Please check your credentials.",
        "error",
      );
      return;
    }

    if (emailError.value != null || passwordError.value != null) {
      customSnackBar(
        "Invalid Credentials!",
        "Please check your credentials. Validation failed.",
        "error",
      );
      return;
    }
    // Add seconds delay
    isLoading.value = true;
    await Future.delayed(const Duration(seconds: 2));
    await appwriteService.signUp(name.value, email.value, password.value);

    // Check if the user is authenticated
    if (appwriteService.getSession.value.id != null) {
      Get.log("User is authenticated.");
      isLoading.value = false; // Ensure isLoading is reset here
    } else {
      Get.log("Authentication failed. No session ID.");
      isLoading.value = false; // Ensure isLoading is reset here
    }
    isLoading.value = false; // Reset isLoading after login process
  }
}
