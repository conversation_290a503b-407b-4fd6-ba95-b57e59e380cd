import 'package:shadfdpcardgame/app/utils/custom_text.dart';
import 'package:shadfdpcardgame/app/modules/settings/settings_controller.dart';
import 'package:flutter/material.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart' as shadcn;
import 'package:get/get.dart';

import 'register_controller.dart';

final SettingsController settingsController = Get.find<SettingsController>();

class RegisterView extends GetView<RegisterController> {
  const RegisterView({super.key});
  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return Stack(
      children: [
        // Background image
        Container(
          height: size.height * 0.70,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage('images/logo.webp'),
              fit: BoxFit.cover,
            ),
          ),
        ),
        // Foreground content with AppBar and body
        shadcn.ShadcnApp(
          theme: shadcn.ThemeData(
            colorScheme: settingsController.shadcnTheme(),
            radius: 0.5,
          ),
          home: shadcn.Scaffold(
            backgroundColor: Colors.transparent, // Make Scaffold transparent
            headers: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    shadcn.IconButton.ghost(
                      onPressed: () {
                        Get.back();
                      },
                      density: shadcn.ButtonDensity.icon,
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                  ],
                ),
              ),
            ],
            child: Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                height: size.height * 0.50,
                decoration: BoxDecoration(
                  color: shadcn.Theme.of(context).colorScheme.background,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                ),
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: SingleChildScrollView(
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(maxWidth: 750),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            shadcn.TextField(
                              placeholder: Text('Enter your first name'),
                              onChanged:
                                  (value) => controller.validateName(value),
                            ),
                            Obx(
                              () => CustomErrorText(
                                text: controller.nameError.value,
                              ),
                            ),
                            SizedBox(height: 5),
                            shadcn.TextField(
                              placeholder: Text('Enter your email'),
                              onChanged:
                                  (value) => controller.validateEmail(value),
                            ),
                            Obx(
                              () => CustomErrorText(
                                text: controller.emailError.value,
                              ),
                            ),
                            const SizedBox(height: 5),
                            shadcn.TextField(
                              placeholder: Text('Enter your password'),
                              onChanged:
                                  (value) => controller.validatePassword(value),
                              features: [
                                shadcn.InputFeature.passwordToggle(
                                  mode: shadcn.PasswordPeekMode.toggle,
                                ),
                              ],
                            ),
                            Obx(
                              () => CustomErrorText(
                                text: controller.passwordError.value,
                              ),
                            ),
                            const SizedBox(height: 5),
                            Obx(
                              () => Row(
                                children: [
                                  Expanded(
                                    child:
                                        controller.isLoading.value
                                            ? Column(
                                              children: [
                                                shadcn.CircularProgressIndicator(),
                                              ],
                                            )
                                            : shadcn.PrimaryButton(
                                              onPressed: () {
                                                controller.getRegister();
                                              },
                                              child: Text('Register'),
                                            ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 5),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const CustomText(
                                  text: "Already have an account?",
                                ),
                                SizedBox(width: 5),
                                TextButton(
                                  onPressed: () {
                                    Get.toNamed('/login');
                                  },
                                  child: const Text('Login...'),
                                ),
                              ],
                            ),
                            // SizedBox(height: 150),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
