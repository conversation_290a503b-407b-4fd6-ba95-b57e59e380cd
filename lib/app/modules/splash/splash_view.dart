import 'package:flutter/material.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart' as shadcn;
import 'package:get/get.dart';

import 'splash_controller.dart';

class SplashView extends GetView<SplashController> {
  const SplashView({super.key});
  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return Scaffold(
      body: Column(
        children: [
          SizedBox(
            width: size.width,
            height: size.height * 0.5,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text("Welcome!").h3,
                  <PERSON>zed<PERSON><PERSON>(height: 20),
                  Text("FDP").p,
                ],
              ),
            ),
          ),

          SizedBox(
            width: size.width,
            height: size.height * 0.5,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [const shadcn.CircularProgressIndicator(size: 16)],
            ),
          ),
        ],
      ),
    );
  }
}
