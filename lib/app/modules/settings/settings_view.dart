import 'package:shadfdpcardgame/app/utils/custom_text.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart' as shadcn;
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'settings_controller.dart';

class SettingsView extends GetView<SettingsController> {
  const SettingsView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SafeArea(
        child: shadcn.Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Row(
                children: [
                  Obx(
                    () => shadcn.Switch(
                      value: controller.isDarkMode.value,
                      onChanged: (value) {
                        controller.isDarkMode.value = value;
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  CustomText(text: 'Dark Mode'),
                ],
              ),
              const Spacer(),
              shadcn.PrimaryButton(
                child: Text('Function'),
                onPressed: () {
                  controller.getFunction();
                },
              ),
              Sized<PERSON>ox(height: 16),
              shadcn.PrimaryButton(
                child: Text('Logout'),
                onPressed: () {
                  controller.logoutUser();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
