import 'package:get/get.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart' as shadcn;
import 'package:shadfdpcardgame/app/data/appwrite/appwrite_service.dart';

final appwriteService = Get.find<AppwriteService>();

class SettingsController extends GetxController {
  final isDarkMode = false.obs;

  shadcn.ColorScheme shadcnTheme() {
    if (isDarkMode.value) {
      return shadcn.ColorSchemes.darkGray();
    } else {
      return shadcn.ColorSchemes.lightGray();
    }
  }

  void logoutUser() async {
    try {
      await appwriteService.logout();
      Get.offAllNamed('/login');
    } catch (e) {
      Get.log("Error logging out: $e");
    }
  }

  void getFunction() async {
    // Execute the function
    try {
      final response = await appwriteService.functions.createExecution(
        functionId: '6808542e001f939c7041',
      );
      final jsonResponse = response.toMap();
      Get.log(jsonResponse.toString());
    } catch (error) {
      Get.log(error.toString());
    }
  }
}
