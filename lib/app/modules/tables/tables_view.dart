import 'package:shadfdpcardgame/app/modules/settings/settings_controller.dart';
import 'package:shadfdpcardgame/app/utils/custom_text.dart';
import 'package:shadfdpcardgame/app/utils/room_card.dart';
import 'package:flutter/material.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart' as shadcn;

import 'package:get/get.dart';

import 'tables_controller.dart';

class TablesView extends GetView<TablesController> {
  const TablesView({super.key});
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Row(
                children: [
                  shadcn.Avatar(
                    backgroundColor: Colors.red,
                    initials: shadcn.Avatar.getInitials('sunarya-thito'),
                    provider: const NetworkImage(
                      'https://avatars.githubusercontent.com/u/64018564?v=4',
                    ),
                  ),
                  SizedBox(width: 16),
                  CustomText(
                    text: "Welcome, ${appwriteService.getUser.value.name}!",
                  ).xLarge(),
                ],
              ),
              const SizedBox(height: 30),
              Expanded(
                child: Obx(
                  () => ListView.builder(
                    itemCount: controller.rooms.length,
                    itemBuilder: (context, index) {
                      final room = controller.rooms[index];
                      return RoomCard(room: room);
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
