import 'package:appwrite/appwrite.dart';
import 'package:shadfdpcardgame/app/data/appwrite/appwrite_service.dart';
import 'package:shadfdpcardgame/app/data/appwrite/models/rooms_model.dart';
import 'package:get/get.dart';

class TablesController extends GetxController {
  final appwriteService = Get.find<AppwriteService>();
  final RxList<Room> rooms = <Room>[].obs;
  late RealtimeSubscription? _tablesSubscription;

  @override
  void onInit() async {
    super.onInit();
    await appwriteService.isAuthenticated();
    await _fetchInitialRooms();
    _listenToRoomUpdates();
  }

  Future<void> _fetchInitialRooms() async {
    Get.log('Fetching initial rooms...');
    try {
      final response = await appwriteService.databases.listDocuments(
        databaseId: '68211293000a791a1f0a',
        collectionId: '6821129f0027192c842c',
      );
      response.documents.map((doc) => Get.log(doc.data.toString())).toList();
      rooms.value =
          response.documents.map((doc) => Room.fromJson(doc.data)).toList();
    } catch (e) {
      Get.log('Error fetching rooms: $e');
    }
  }

  void _listenToRoomUpdates() {
    _tablesSubscription = appwriteService.realtimeTables.subscribe([
      "databases.68211293000a791a1f0a.collections.6821129f0027192c842c",
    ]);
    _tablesSubscription!.stream.listen(
      (message) {
        try {
          final event = message.events.first;
          final payload = message.payload;
          final room = Room.fromJson(payload);
          if (event.contains('create')) {
            rooms.add(room);
          } else if (event.contains('update')) {
            final index = rooms.indexWhere((r) => r.id == room.id);
            if (index != -1) rooms[index] = room;
          } else if (event.contains('delete')) {
            rooms.removeWhere((r) => r.id == room.id);
          }
        } catch (e) {
          Get.log('Error processing realtime update: $e');
        }
      },
      onError: (error) {
        Get.log('Realtime error: $error');
      },
    );
  }

  @override
  void onClose() {
    super.onClose();
    _tablesSubscription!.close();
  }
}
