import 'package:flutter/material.dart';
import 'package:playing_cards/playing_cards.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:shadfdpcardgame/app/utils/players_circle.dart';
import 'game_controller.dart';
import 'dart:ui';
import 'package:shadcn_flutter/shadcn_flutter.dart' as shadcn;

class MyCustomScrollBehavior extends MaterialScrollBehavior {
  // Override the scroll behavior for all platforms, or add conditional logic based on the platform
  @override
  Set<PointerDeviceKind> get dragDevices => {
    PointerDeviceKind.touch,
    PointerDeviceKind.mouse,
    // Add other device types if you need them
  };
}

class GameView extends GetView<GameController> {
  const GameView({super.key});
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final isLargeScreen = size.width > 700;
    return Scaffold(
      body: SmartRefresher(
        controller: controller.refreshController,
        enablePullDown: true,
        onRefresh: () async {
          await controller.refreshScreen();
        },
        child: SingleChildScrollView(
          child: Center(
            child: Container(
              constraints: const BoxConstraints(maxWidth: 700),
              // color: Colors.white,
              child: Column(
                children: [
                  Stack(
                    children: [
                      Center(
                        child: Material(
                          elevation: 5,
                          child: Image.asset(
                            'images/table_2.webp',
                            // width: double.infinity,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),

                      Positioned(
                        bottom: 30,
                        right: 10,
                        child: Container(
                          height:
                              isLargeScreen
                                  ? 100
                                  : size.width / 5 <= 100
                                  ? 100
                                  : size.width / 5,
                          color: Colors.transparent,
                          child: Visibility(
                            visible: true,
                            child: Obx(
                              () => PlayingCardView(
                                card:
                                    controller.lastDroppedCard.value != null
                                        ? controller.lastDroppedCard.value!
                                        : PlayingCard(
                                          Suit.hearts,
                                          CardValue.ace,
                                        ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6.0),
                                ),
                                showBack:
                                    controller.lastDroppedCard.value == null
                                        ? true
                                        : false,
                                elevation: 1.0,
                              ),
                            ),
                          ),
                        ),
                      ),

                      const Positioned(
                        bottom: 5,
                        right: 15,
                        child: Center(
                          child: Text(
                            "AddCode",
                            style: TextStyle(color: Colors.white, fontSize: 16),
                          ),
                        ),
                      ),

                      // Target Position.
                      true // add code later
                          ? Positioned(
                            top:
                                isLargeScreen
                                    ? 190
                                    : (190 * (size.width / 700)),
                            left:
                                isLargeScreen
                                    ? 202
                                    : (202 * (size.width / 700)),
                            child: DragTarget<PlayingCard>(
                              onWillAcceptWithDetails:
                                  (data) =>
                                      true, // Decide if the data will be accepted
                              onAcceptWithDetails: (data) {
                                controller.sendCards(data.data);
                                Get.log(data.data.toString());
                              },
                              builder: (context, candidateData, rejectedData) {
                                return Container(
                                  height:
                                      isLargeScreen
                                          ? 250
                                          : (250 *
                                              (size.width /
                                                  700)), // Set as needed
                                  width:
                                      isLargeScreen
                                          ? 290
                                          : (290 *
                                              (size.width /
                                                  700)), // Set as needed
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(
                                      0.5,
                                    ), // White color with opacity
                                    shape:
                                        BoxShape
                                            .circle, // Make the container a circle
                                  ), // Set background color if needed, transparent for just an outline
                                  child: const Center(
                                    child: Text(
                                      'Drop Here',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          )
                          : const SizedBox.shrink(),
                      PlayerCircle(
                        url:
                            "https://avatars.githubusercontent.com/u/64018555?v=3",
                        isVisible: true,
                        isLargeScreen: isLargeScreen,
                        size: size,
                        top: 0.171,
                        left: 0.20,
                      ),
                      PlayerCircle(
                        isLargeScreen: isLargeScreen,
                        size: size,
                        top: 0.05,
                        left: 0.448,
                      ),
                      PlayerCircle(
                        isLargeScreen: isLargeScreen,
                        size: size,
                        top: 0.155,
                        left: 0.69,
                      ),
                      PlayerCircle(
                        isLargeScreen: isLargeScreen,
                        size: size,
                        top: 0.417,
                        left: 0.10,
                      ),
                      PlayerCircle(
                        isVisible: true,
                        isLargeScreen: isLargeScreen,
                        size: size,
                        top: 0.382,
                        left: 0.80,
                      ),
                      PlayerCircle(
                        isLargeScreen: isLargeScreen,
                        size: size,
                        top: 0.642,
                        left: 0.715,
                      ),
                      PlayerCircle(
                        isLargeScreen: isLargeScreen,
                        size: size,
                        top: 0.67,
                        left: 0.21,
                      ),
                      PlayerCircle(
                        isLargeScreen: isLargeScreen,
                        size: size,
                        top: 0.75,
                        left: 0.454,
                      ),
                    ],
                  ),
                  controller.isConnected.value
                      ? SizedBox(child: LinearProgressIndicator())
                      : SizedBox.shrink(),
                  const Padding(
                    padding: EdgeInsets.only(top: 24.0, left: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Your Cards:",
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: SizedBox(
                      height: 110,
                      child: ScrollConfiguration(
                        behavior: MyCustomScrollBehavior(),
                        child: Obx(
                          () => ListView(
                            physics: const AlwaysScrollableScrollPhysics(),
                            scrollDirection: Axis.horizontal,
                            children:
                                controller.cardsData
                                    .map(
                                      (e) => LongPressDraggable(
                                        onDragStarted: () => true,
                                        onDragEnd: (details) => false,
                                        data: e.toPlayingCard(),
                                        delay: const Duration(
                                          milliseconds: 150,
                                        ),
                                        feedback: Material(
                                          type: MaterialType.transparency,
                                          child: ConstrainedBox(
                                            constraints: const BoxConstraints(
                                              maxWidth: 100,
                                              maxHeight: 150,
                                            ),
                                            child: Opacity(
                                              opacity: 0.5,
                                              child: PlayingCardView(
                                                card: e.toPlayingCard(),
                                              ),
                                            ),
                                          ),
                                        ),
                                        childWhenDragging: Container(),
                                        child: PlayingCardView(
                                          card: e.toPlayingCard(),
                                          elevation: 1.5,
                                        ),
                                      ),
                                    )
                                    .toList(),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const Padding(
                    padding: EdgeInsets.only(top: 8.0, left: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Bet how many turns you are going to win...",
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0, left: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            shadcn.SecondaryButton(
                              onPressed: () {
                                controller.decrement();
                              },
                              shape: shadcn.ButtonShape.circle,
                              child: const Icon(Icons.remove),
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 16),
                              child: Obx(
                                () => Text(
                                  controller.bet.value.toString(),
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                            shadcn.PrimaryButton(
                              onPressed: () {
                                controller.increment();
                              },
                              shape: shadcn.ButtonShape.circle,
                              child: const Icon(Icons.add),
                            ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24.0),
                          child: shadcn.PrimaryButton(
                            onPressed: () {},
                            child: const Text('Confirm'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
