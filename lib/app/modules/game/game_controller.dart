import 'dart:convert';
import 'package:appwrite/appwrite.dart';
import 'package:appwrite/enums.dart';
import 'package:get/get.dart';
import 'package:playing_cards/playing_cards.dart';
import 'package:shadfdpcardgame/app/data/appwrite/appwrite_service.dart';
import 'package:shadfdpcardgame/app/data/appwrite/models/cards_model.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

class GameController extends GetxController {
  final bet = 0.obs;
  final appwriteService = Get.find<AppwriteService>();
  final RxList<SimpleCardData> cardsData = <SimpleCardData>[].obs;
  final RxList<PlayingCard> userCards = <PlayingCard>[].obs;
  final lastDroppedCard = Rxn<PlayingCard>();
  RealtimeSubscription? _cardsSubscription;
  RealtimeSubscription? _lastDroppedCardsSubscription;
  RxBool isConnected = false.obs;
  RefreshController refreshController = RefreshController(
    initialRefresh: false,
  );

  @override
  void onInit() async {
    super.onInit();
    await appwriteService.isAuthenticated();
    await _fetchLastDroppedCard();
    await _fetchInitialCards();
    _listenToLastDroppedCardUpdates();
    _listenToCardsUpdates();
  }

  Future<void> refreshScreen() {
    Future.delayed(const Duration(seconds: 5));
    refreshController.refreshCompleted();
    return Future.value();
  }

  Future<void> _fetchLastDroppedCard() async {
    try {
      final response = await appwriteService.databases.listDocuments(
        databaseId: '68211293000a791a1f0a',
        collectionId: '682785f4001ec7b3015c',
        queries: [Query.orderDesc('\$updatedAt'), Query.limit(1)],
      );
      if (response.documents.isNotEmpty) {
        final card = SimpleCardData.fromJson(response.documents.first.data);
        lastDroppedCard.value = card.toPlayingCard();
      }
    } catch (e) {
      Get.log("error to get dropped card: $e");
    }
  }

  Future<void> _fetchInitialCards() async {
    Get.log('Fetching initial rooms...');
    try {
      final response = await appwriteService.databases.listDocuments(
        databaseId: '68211293000a791a1f0a',
        collectionId: '68262c7b002f41591e38',
      );
      response.documents.map((doc) => Get.log(doc.data.toString())).toList();
      cardsData.value =
          response.documents
              .map((doc) => SimpleCardData.fromJson(doc.data))
              .toList()
              .cast<SimpleCardData>();
    } catch (e) {
      Get.log('Error fetching cards: $e');
    }
  }

  void _listenToLastDroppedCardUpdates() {
    _lastDroppedCardsSubscription = appwriteService.realtimeUserDroppedCards
        .subscribe([
          'databases.68211293000a791a1f0a.collections.682785f4001ec7b3015c.documents',
        ]);
    _lastDroppedCardsSubscription!.stream.listen(
      (data) {
        final event = data.events.first;
        final payload = data.payload;
        final card = SimpleCardData.fromJson(payload);
        if (event.contains('create')) {
          lastDroppedCard.value = card.toPlayingCard();
        }
      },
      onError: (error) {
        Get.log('Realtime error: $error');
      },
    );
  }

  void _listenToCardsUpdates() {
    _cardsSubscription = appwriteService.realtimeUserCards.subscribe([
      'databases.68211293000a791a1f0a.collections.68262c7b002f41591e38.documents',
    ]);
    _cardsSubscription!.stream.listen(
      (data) {
        if (!isConnected.value) {
          isConnected.value = true;
        }
        try {
          final event = data.events.first;
          final payload = data.payload;
          final card = SimpleCardData.fromJson(payload);
          if (event.contains('create')) {
            cardsData.add(card);
          } else if (event.contains('update')) {
            final index = cardsData.indexWhere((r) => r.id == card.id);
            if (index != -1) cardsData[index] = card;
          } else if (event.contains('delete')) {
            cardsData.removeWhere((r) => r.id == card.id);
          }
        } catch (e) {
          Get.log('Error processing realtime update: $e');
        }
      },
      onError: (error) {
        Get.log('Realtime error: $error');
      },
    );
  }

  void sendCards(PlayingCard card) async {
    try {
      final cardJson = {
        'suit': card.suit.toString().split('.').last,
        'cardValue': card.value.toString().split('.').last,
      };

      final response = await appwriteService.functions.createExecution(
        method: ExecutionMethod.pOST,
        functionId: '6827a7f8000767dbf476',
        body: jsonEncode(cardJson),
      );
      final jsonResponse = response.toMap();
      Get.log(jsonResponse.toString());
    } catch (error) {
      Get.log('ERROR:$error');
    }
  }

  @override
  void onClose() {
    super.onClose();
    _cardsSubscription?.close();
    _lastDroppedCardsSubscription?.close();
    super.dispose();
  }

  void increment() async {
    try {
      bet.value++;
    } catch (e) {
      Get.log("Error logging out: $e");
    }
  }

  void decrement() async {
    try {
      if (bet.value > 0) {
        bet.value--;
      }
    } catch (e) {
      Get.log("Error logging out: $e");
    }
  }
}
