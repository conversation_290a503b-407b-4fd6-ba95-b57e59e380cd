import 'package:flutter/material.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart' as shadcn;

class PlayerCircle extends StatelessWidget {
  const PlayerCircle({
    super.key,
    required this.isLargeScreen,
    required this.size,
    this.top = 0,
    this.left = 0,
    this.isVisible = false,
    this.url,
  });

  final bool isLargeScreen;
  final Size size;
  final double top;
  final double left;
  final bool isVisible;
  final String? url;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: isVisible,
      child: Positioned(
        // Keep `top` position the same when the screen is wider than 700,
        // otherwise scale it based on screen width.
        top: isLargeScreen ? 700 * top : size.width * top,

        // For `left`, the intention is to keep the widget in the same horizontal
        // position relative to the screen size. If the screen is larger, we want to
        // adjust the position proportionally to keep it visually consistent.
        left: isLargeScreen ? 700 * left : size.width * left,

        child: shadcn.Avatar(
          backgroundColor: Colors.white,
          initials: shadcn.Avatar.getInitials('sun<PERSON>a-th<PERSON>'),
          provider: shadcn.NetworkImage(
            url ?? 'https://avatars.githubusercontent.com/u/64018564?v=4',
          ),
          size: isLargeScreen ? 60 : size.width * 0.09,
        ),
      ),
    );
  }
}
