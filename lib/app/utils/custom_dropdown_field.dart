import 'package:shadfdpcardgame/app/utils/app_colors.dart';
import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';

class CustomDropDownField extends StatelessWidget {
  final IconData? icon;
  final List<DropDownValueModel>
  dropDownList; // Accept dropdown items as a parameter
  final Function(DropDownValueModel?)? onChanged; // Callback for value change
  final String? labelText;

  const CustomDropDownField({
    super.key,
    this.icon,
    required this.dropDownList,
    this.onChanged,
    this.labelText,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: DropDownTextField(
        controller: SingleValueDropDownController(),
        clearOption: true,
        enableSearch: false,
        dropDownItemCount: dropDownList.length,
        dropDownList: dropDownList, // Use the provided dropdown list
        onChanged:
            (value) => onChanged?.call(
              value as DropDownValueModel?,
            ), // Wrap to match the expected type
        textFieldDecoration: InputDecoration(
          contentPadding: const EdgeInsets.symmetric(horizontal: 24.0),
          focusedBorder: OutlineInputBorder(
            borderSide: const BorderSide(color: AppColors.skyBlue, width: 2.0),
            borderRadius: BorderRadius.circular(50.0),
          ),
          prefixIcon: Icon(icon),
          labelText: labelText,
          labelStyle: const TextStyle(color: Colors.black),
          isDense: true,
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(50)),
        ),
      ),
    );
  }
}
