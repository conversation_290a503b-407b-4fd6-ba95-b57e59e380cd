import 'package:flutter/widgets.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart' as shadcn;

class CustomText extends StatelessWidget {
  final String text;
  const CustomText({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: TextStyle(color: shadcn.Theme.of(context).colorScheme.foreground),
    );
  }
}

class CustomErrorText extends StatelessWidget {
  final String? text;
  const CustomErrorText({super.key, this.text});

  @override
  Widget build(BuildContext context) {
    return Text(
      text ?? '',
      style: TextStyle(color: shadcn.Colors.red, fontSize: 12),
    );
  }
}
