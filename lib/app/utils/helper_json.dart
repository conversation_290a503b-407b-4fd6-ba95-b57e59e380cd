import 'dart:convert';

dynamic decodeNested<PERSON>son(dynamic item) {
  if (item is String) {
    try {
      // Try to decode the string as JSON
      final decoded = jsonDecode(item);
      // If decoding succeeds, recursively decode in case it's nested
      return decodeNested<PERSON><PERSON>(decoded);
    } catch (e) {
      // If decoding fails, return the original string (it's not JSON)
      return item;
    }
  } else if (item is Map) {
    // If it's a Map, decode each value
    item.forEach((key, value) {
      item[key] = decodeNestedJson(value);
    });
  } else if (item is List) {
    // If it's a List, decode each element
    for (int i = 0; i < item.length; i++) {
      item[i] = decodeNestedJson(item[i]);
    }
  }
  // Return the item (modified if it was a Map or List, unmodified otherwise)
  return item;
}
