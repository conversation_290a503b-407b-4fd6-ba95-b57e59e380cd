import 'package:playing_cards/playing_cards.dart';

PlayingCard getPlayingCardFromString(String cardSuit, String cardValue) {
  return PlayingCard(
    getSuitFromString(cardSuit),
    getCardValueFromString(cardValue),
  );
}

Suit getSuitFromString(String suitString) {
  switch (suitString.toLowerCase()) {
    case 'hearts':
      return Suit.hearts;
    case 'diamonds':
      return Suit.diamonds;
    case 'clubs':
      return Suit.clubs;
    case 'spades':
      return Suit.spades;
    default:
      throw Exception('Invalid suit type');
  }
}

CardValue getCardValueFromString(String cardValueString) {
  switch (cardValueString.toLowerCase()) {
    case 'ace':
      return CardValue.ace;
    case 'two':
      return CardValue.two;
    case 'three':
      return CardValue.three;
    case 'four':
      return CardValue.four;
    case 'five':
      return CardValue.five;
    case 'six':
      return CardValue.six;
    case 'seven':
      return CardValue.seven;
    case 'eight':
      return CardValue.eight;
    case 'nine':
      return CardValue.nine;
    case 'ten':
      return CardValue.ten;
    case 'jack':
      return CardValue.jack;
    case 'queen':
      return CardValue.queen;
    case 'king':
      return CardValue.king;

    default:
      throw Exception('Invalid card value');
  }
}
