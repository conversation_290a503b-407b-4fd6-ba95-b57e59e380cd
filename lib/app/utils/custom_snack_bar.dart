import 'package:flutter/material.dart';
import 'package:get/get.dart';

void customSnackBar(
  String title,
  String message, [
  String situation = 'success',
]) {
  Get.snackbar(
    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
    title,
    message,
    snackPosition: SnackPosition.BOTTOM,
    backgroundColor:
        situation == 'error'
            ? Colors.red.withValues(alpha: 0.7)
            : Colors.green.withValues(alpha: 0.7),
    colorText: Colors.white,
    margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 30),
    borderRadius: 10,
    isDismissible: true,
    duration: const Duration(seconds: 3),
    overlayBlur: 10.0, // Value for blur effect
  );
}
