import 'package:flutter/material.dart';

class AppColors {
  // Create a beautiful gradient primary color.

  // Create a beautiful gradient primary color.
  static const Gradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  static const Color fdpImage = Color.fromARGB(255, 5, 15, 60);
  static const Color primary = Color.fromARGB(255, 255, 57, 57);
  static const Color primaryDark = Color(0xFF689F38);
  static const Color primaryLight = Color(0xFFDCEDC8);
  static const Color accent = Color(0xFFCDDC39);
  static const Color accentDark = Color(0xFFAFB42B);
  static const Color accentLight = Color(0xFFF0F4C3);
  static const Color background = Color(0xFFFFFFFF);
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color divider = Color(0xFFBDBDBD);
  // Colors from image
  static const Color darkBlue = Color(0xFF152954);
  static const Color lightBeige = Color(0xFFE8E2D3);
  static const Color deepRed = Color(0xFFA5243F);
  static const Color salmon = Color(0xFFD2846E);
  static const Color skyBlue = Color(0xFF28A6C7);
  static const Color darkNavy = Color(0xFF0C0A22);
  static const Color mediumBlue = Color(0xFF1D5584);
}
