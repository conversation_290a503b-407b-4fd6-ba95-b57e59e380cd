import 'package:shadfdpcardgame/app/data/appwrite/models/rooms_model.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';
import 'package:get/get.dart';

class RoomCard extends StatelessWidget {
  final Room room;

  const RoomCard({super.key, required this.room});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Card(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  room.gameType,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  room.status.toUpperCase(),
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color:
                        room.status == "open"
                            ? const Color.fromARGB(255, 150, 250, 200)
                            : room.status == "ongoing"
                            ? const Color.fromARGB(255, 250, 250, 40)
                            : const Color.fromARGB(255, 250, 65, 65),
                  ),
                ),
                PrimaryBadge(
                  child: Text(
                    "${room.currentPlayers.length}/${room.maxPlayers}",
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(room.description),
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(Icons.attach_money, size: 30),
                const SizedBox(width: 2),
                Text(
                  room.amount.toStringAsFixed(2),
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                PrimaryButton(
                  size: ButtonSize.small,
                  onPressed: () {
                    // Handle join room action
                    showDialog(
                      context: context,
                      builder:
                          (context) => AlertDialog(
                            title: Text('Joining Room: ${room.gameType}'),
                            content: Column(
                              children: [
                                Row(
                                  children: [
                                    SizedBox(
                                      width: Get.mediaQuery.size.width * 0.7,
                                      child: Text(
                                        "We will debit the amount below from your account:",
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 8),
                                Row(
                                  children: [
                                    SizedBox(
                                      width: Get.mediaQuery.size.width * 0.7,
                                      child: Text(
                                        "\$${room.amount.toStringAsFixed(2)}",
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 8),
                                Row(
                                  children: [
                                    SizedBox(
                                      width: Get.mediaQuery.size.width * 0.7,
                                      child: Text("Please, confirm it..."),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.pop(context),
                                child: const Text('Cancel'),
                              ),
                              PrimaryButton(
                                onPressed: () {
                                  // Perform join logic here
                                  Navigator.pop(context);
                                },
                                child: const Text('Confirm'),
                              ),
                            ],
                          ),
                    );
                  },
                  child: const Text(
                    'Join Game',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Text(
              'Players:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 25,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: room.currentPlayers.length,
                itemBuilder: (context, index) {
                  final player = room.currentPlayers[index];
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: SecondaryBadge(
                      child: Text(player, style: TextStyle(fontSize: 12)),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
