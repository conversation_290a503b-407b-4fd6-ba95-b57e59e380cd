class CreateAccountModel {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? name;
  final String? password;
  final String? hash;
  final dynamic hashOptions;
  final DateTime? registration;
  final bool? status;
  final List<dynamic>? labels;
  final DateTime? passwordUpdate;
  final String? email;
  final String? phone;
  final bool? emailVerification;
  final bool? phoneVerification;
  final bool? mfa;
  final Map<String, dynamic>? prefs;
  final List<dynamic>? targets;
  final DateTime? accessedAt;

  CreateAccountModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.name,
    this.password,
    this.hash,
    this.hashOptions,
    this.registration,
    this.status,
    this.labels,
    this.passwordUpdate,
    this.email,
    this.phone,
    this.emailVerification,
    this.phoneVerification,
    this.mfa,
    this.prefs,
    this.targets,
    this.accessedAt,
  });

  factory CreateAccountModel.fromJson(Map<String, dynamic> json) {
    return CreateAccountModel(
      id: json['\$id'],
      createdAt:
          json['\$createdAt'] != null
              ? DateTime.parse(json['\$createdAt'])
              : null,
      updatedAt:
          json['\$updatedAt'] != null
              ? DateTime.parse(json['\$updatedAt'])
              : null,
      name: json['name'],
      password: json['password'],
      hash: json['hash'],
      hashOptions: json['hashOptions'],
      registration:
          json['registration'] != null
              ? DateTime.parse(json['registration'])
              : null,
      status: json['status'],
      labels: List.from(json['labels'] ?? []),
      passwordUpdate:
          json['passwordUpdate'] != null
              ? DateTime.parse(json['passwordUpdate'])
              : null,
      email: json['email'],
      phone: json['phone'],
      emailVerification: json['emailVerification'],
      phoneVerification: json['phoneVerification'],
      mfa: json['mfa'],
      prefs: Map<String, dynamic>.from(json['prefs']?['data'] ?? {}),
      targets: List.from(json['targets'] ?? []),
      accessedAt:
          json['accessedAt'] != null
              ? DateTime.parse(json['accessedAt'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '\$id': id,
      '\$createdAt': createdAt?.toIso8601String(),
      '\$updatedAt': updatedAt?.toIso8601String(),
      'name': name,
      'password': password,
      'hash': hash,
      'hashOptions': hashOptions,
      'registration': registration?.toIso8601String(),
      'status': status,
      'labels': labels,
      'passwordUpdate': passwordUpdate?.toIso8601String(),
      'email': email,
      'phone': phone,
      'emailVerification': emailVerification,
      'phoneVerification': phoneVerification,
      'mfa': mfa,
      'prefs': {'data': prefs},
      'targets': targets,
      'accessedAt': accessedAt?.toIso8601String(),
    };
  }
}
