class GetUserSession {
  final String? id;
  final String? createdAt;
  final String? updatedAt;
  final String? userId;
  final String? expire;
  final String? provider;
  final String? providerUid;
  final String? providerAccessToken;
  final String? providerAccessTokenExpiry;
  final String? providerRefreshToken;
  final String? ip;
  final String? osCode;
  final String? osName;
  final String? osVersion;
  final String? clientType;
  final String? clientCode;
  final String? clientName;
  final String? clientVersion;
  final String? clientEngine;
  final String? clientEngineVersion;
  final String? deviceName;
  final String? deviceBrand;
  final String? deviceModel;
  final String? countryCode;
  final String? countryName;
  final bool? current;
  final List<String>? factors;
  final String? secret;
  final String? mfaUpdatedAt;

  GetUserSession({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.userId,
    this.expire,
    this.provider,
    this.providerUid,
    this.providerAccessToken,
    this.providerAccessTokenExpiry,
    this.providerRefreshToken,
    this.ip,
    this.osCode,
    this.osName,
    this.osVersion,
    this.clientType,
    this.clientCode,
    this.clientName,
    this.clientVersion,
    this.clientEngine,
    this.clientEngineVersion,
    this.deviceName,
    this.deviceBrand,
    this.deviceModel,
    this.countryCode,
    this.countryName,
    this.current,
    this.factors,
    this.secret,
    this.mfaUpdatedAt,
  });

  factory GetUserSession.fromJson(Map<String, dynamic> json) {
    return GetUserSession(
      id: json['\$id'],
      createdAt: json['\$createdAt'],
      updatedAt: json['\$updatedAt'],
      userId: json['userId'],
      expire: json['expire'],
      provider: json['provider'],
      providerUid: json['providerUid'],
      providerAccessToken: json['providerAccessToken'],
      providerAccessTokenExpiry: json['providerAccessTokenExpiry'],
      providerRefreshToken: json['providerRefreshToken'],
      ip: json['ip'],
      osCode: json['osCode'],
      osName: json['osName'],
      osVersion: json['osVersion'],
      clientType: json['clientType'],
      clientCode: json['clientCode'],
      clientName: json['clientName'],
      clientVersion: json['clientVersion'],
      clientEngine: json['clientEngine'],
      clientEngineVersion: json['clientEngineVersion'],
      deviceName: json['deviceName'],
      deviceBrand: json['deviceBrand'],
      deviceModel: json['deviceModel'],
      countryCode: json['countryCode'],
      countryName: json['countryName'],
      current: json['current'],
      factors:
          (json['factors'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList(),
      secret: json['secret'],
      mfaUpdatedAt: json['mfaUpdatedAt'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '\$id': id,
      '\$createdAt': createdAt,
      '\$updatedAt': updatedAt,
      'userId': userId,
      'expire': expire,
      'provider': provider,
      'providerUid': providerUid,
      'providerAccessToken': providerAccessToken,
      'providerAccessTokenExpiry': providerAccessTokenExpiry,
      'providerRefreshToken': providerRefreshToken,
      'ip': ip,
      'osCode': osCode,
      'osName': osName,
      'osVersion': osVersion,
      'clientType': clientType,
      'clientCode': clientCode,
      'clientName': clientName,
      'clientVersion': clientVersion,
      'clientEngine': clientEngine,
      'clientEngineVersion': clientEngineVersion,
      'deviceName': deviceName,
      'deviceBrand': deviceBrand,
      'deviceModel': deviceModel,
      'countryCode': countryCode,
      'countryName': countryName,
      'current': current,
      'factors': factors,
      'secret': secret,
      'mfaUpdatedAt': mfaUpdatedAt,
    };
  }
}
