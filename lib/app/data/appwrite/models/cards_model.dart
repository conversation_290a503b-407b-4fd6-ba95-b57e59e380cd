import 'package:playing_cards/playing_cards.dart';
import 'package:shadfdpcardgame/app/utils/helper_cards.dart';

class SimpleCardData {
  final String id;
  final int round;
  final CardValue cardValue;
  final Suit suit;
  final String roomId;
  final String? userId;

  SimpleCardData({
    required this.id,
    required this.round,
    required this.cardValue,
    required this.suit,
    required this.roomId,
    this.userId,
  });

  factory SimpleCardData.fromJson(Map<String, dynamic> json) {
    return SimpleCardData(
      id: json['\$id'],
      round: json['round'],
      cardValue: getCardValueFromString(json['cardValue']),
      suit: getSuitFromString(json['suit']),
      roomId: json['roomId']['\$id'],
      userId: json['userId'] != null ? json['userId']['\$id'] : null,
    );
  }

  PlayingCard toPlayingCard() {
    return PlayingCard(suit, cardValue);
  }
}
