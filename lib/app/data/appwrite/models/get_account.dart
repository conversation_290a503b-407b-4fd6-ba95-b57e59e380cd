class GetAccountModel {
  String? id;
  String? createdAt;
  String? updatedAt;
  String? name;
  String? registration;
  bool? status;
  List<String>? labels;
  String? passwordUpdate;
  String? email;
  String? phone;
  bool? emailVerification;
  bool? phoneVerification;
  bool? mfa;
  Map<String, dynamic>? prefs;
  List<TargetModel>? targets;
  String? accessedAt;

  GetAccountModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.name,
    this.registration,
    this.status,
    this.labels,
    this.passwordUpdate,
    this.email,
    this.phone,
    this.emailVerification,
    this.phoneVerification,
    this.mfa,
    this.prefs,
    this.targets,
    this.accessedAt,
  });

  GetAccountModel.fromJson(Map<String, dynamic> json) {
    id = json['\$id'];
    createdAt = json['\$createdAt'];
    updatedAt = json['\$updatedAt'];
    name = json['name'];
    registration = json['registration'];
    status = json['status'];
    labels = List<String>.from(json['labels'] ?? []);
    passwordUpdate = json['passwordUpdate'];
    email = json['email'];
    phone = json['phone'];
    emailVerification = json['emailVerification'];
    phoneVerification = json['phoneVerification'];
    mfa = json['mfa'];
    prefs = json['prefs'] ?? {};
    targets =
        (json['targets'] as List?)
            ?.map((e) => TargetModel.fromJson(e))
            .toList();
    accessedAt = json['accessedAt'];
  }

  Map<String, dynamic> toJson() {
    return {
      '\$id': id,
      '\$createdAt': createdAt,
      '\$updatedAt': updatedAt,
      'name': name,
      'registration': registration,
      'status': status,
      'labels': labels,
      'passwordUpdate': passwordUpdate,
      'email': email,
      'phone': phone,
      'emailVerification': emailVerification,
      'phoneVerification': phoneVerification,
      'mfa': mfa,
      'prefs': prefs,
      'targets': targets?.map((e) => e.toJson()).toList(),
      'accessedAt': accessedAt,
    };
  }
}

class TargetModel {
  String? id;
  String? createdAt;
  String? updatedAt;
  String? name;
  String? userId;
  String? providerId;
  String? providerType;
  String? identifier;
  bool? expired;

  TargetModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.name,
    this.userId,
    this.providerId,
    this.providerType,
    this.identifier,
    this.expired,
  });

  TargetModel.fromJson(Map<String, dynamic> json) {
    id = json['\$id'];
    createdAt = json['\$createdAt'];
    updatedAt = json['\$updatedAt'];
    name = json['name'];
    userId = json['userId'];
    providerId = json['providerId'];
    providerType = json['providerType'];
    identifier = json['identifier'];
    expired = json['expired'];
  }

  Map<String, dynamic> toJson() {
    return {
      '\$id': id,
      '\$createdAt': createdAt,
      '\$updatedAt': updatedAt,
      'name': name,
      'userId': userId,
      'providerId': providerId,
      'providerType': providerType,
      'identifier': identifier,
      'expired': expired,
    };
  }
}
