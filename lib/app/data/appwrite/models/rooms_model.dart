import 'package:equatable/equatable.dart';

class Room extends Equatable {
  final String id;
  final String gameType;
  final String description;
  final int maxPlayers;
  final double amount;
  final List<String> currentPlayers;
  final bool passwordProtected;
  final String? roomPassword;
  final String status;

  const Room({
    required this.id,
    required this.gameType,
    required this.description,
    required this.maxPlayers,
    required this.amount,
    required this.currentPlayers,
    this.passwordProtected = false,
    this.roomPassword,
    this.status = 'open',
  });

  factory Room.fromJson(Map<String, dynamic> json) {
    return Room(
      id:
          json['\$id'] as String? ??
          json['id'] as String? ??
          '', // Handle both $id and id
      gameType:
          json['gameType'] as String? ?? '', // Provide default empty string
      description:
          json['description'] as String? ?? '', // Provide default empty string
      maxPlayers:
          (json['maxPlayers'] as num?)?.toInt() ??
          0, // Handle null and provide default
      amount:
          (json['amount'] as num?)?.toDouble() ??
          0.0, // Handle null and provide default
      currentPlayers: List<String>.from(
        (json['currentPlayers'] as List<dynamic>?)?.map((e) => e.toString()) ??
            [],
      ), // Handle null and empty lists
      passwordProtected: json['passwordProtected'] as bool? ?? false,
      roomPassword: json['roomPassword'] as String?,
      status: json['status'] as String? ?? 'open',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'gameType': gameType,
      'description': description,
      'maxPlayers': maxPlayers,
      'amount': amount,
      'currentPlayers': currentPlayers,
      'passwordProtected': passwordProtected,
      'roomPassword': roomPassword,
      'status': status,
    };
  }

  Room copyWith({
    String? id,
    String? gameType,
    String? description,
    int? maxPlayers,
    double? amount,
    List<String>? currentPlayers,
    bool? passwordProtected,
    String? roomPassword,
    String? status,
  }) {
    return Room(
      id: id ?? this.id,
      gameType: gameType ?? this.gameType,
      description: description ?? this.description,
      maxPlayers: maxPlayers ?? this.maxPlayers,
      amount: amount ?? this.amount,
      currentPlayers: currentPlayers ?? this.currentPlayers,
      passwordProtected: passwordProtected ?? this.passwordProtected,
      roomPassword: roomPassword ?? this.roomPassword,
      status: status ?? this.status,
    );
  }

  @override
  List<Object?> get props => [
    id,
    gameType,
    description,
    maxPlayers,
    amount,
    currentPlayers,
    passwordProtected,
    roomPassword,
    status,
  ];

  bool get isFull => currentPlayers.length >= maxPlayers;
  bool get isOpen => status == 'open';
  bool get isPrivate => passwordProtected;
  int get availableSlots => maxPlayers - currentPlayers.length;
}
