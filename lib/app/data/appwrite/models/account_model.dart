class AccountModel {
  String? id;
  String? createdAt;
  String? updatedAt;
  String? userId;
  String? expire;
  String? provider;
  String? providerUid;
  String? providerAccessToken;
  String? providerAccessTokenExpiry;
  String? providerRefreshToken;
  String? ip;
  String? osCode;
  String? osName;
  String? osVersion;
  String? clientType;
  String? clientCode;
  String? clientName;
  String? clientVersion;
  String? clientEngine;
  String? clientEngineVersion;
  String? deviceName;
  String? deviceBrand;
  String? deviceModel;
  String? countryCode;
  String? countryName;
  bool? current;
  List<String>? factors;
  String? secret;
  String? mfaUpdatedAt;

  AccountModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.userId,
    this.expire,
    this.provider,
    this.providerUid,
    this.providerAccessToken,
    this.providerAccessTokenExpiry,
    this.providerRefreshToken,
    this.ip,
    this.osCode,
    this.osName,
    this.osVersion,
    this.clientType,
    this.clientCode,
    this.clientName,
    this.clientVersion,
    this.clientEngine,
    this.clientEngineVersion,
    this.deviceName,
    this.deviceBrand,
    this.deviceModel,
    this.countryCode,
    this.countryName,
    this.current,
    this.factors,
    this.secret,
    this.mfaUpdatedAt,
  });

  AccountModel.fromJson(Map<String, dynamic> json) {
    id = json['$id'];
    createdAt = json['$createdAt'];
    updatedAt = json['$updatedAt'];
    userId = json['userId'];
    expire = json['expire'];
    provider = json['provider'];
    providerUid = json['providerUid'];
    providerAccessToken = json['providerAccessToken'];
    providerAccessTokenExpiry = json['providerAccessTokenExpiry'];
    providerRefreshToken = json['providerRefreshToken'];
    ip = json['ip'];
    osCode = json['osCode'];
    osName = json['osName'];
    osVersion = json['osVersion'];
    clientType = json['clientType'];
    clientCode = json['clientCode'];
    clientName = json['clientName'];
    clientVersion = json['clientVersion'];
    clientEngine = json['clientEngine'];
    clientEngineVersion = json['clientEngineVersion'];
    deviceName = json['deviceName'];
    deviceBrand = json['deviceBrand'];
    deviceModel = json['deviceModel'];
    countryCode = json['countryCode'];
    countryName = json['countryName'];
    current = json['current'];
    factors = json['factors']?.cast<String>();
    secret = json['secret'];
    mfaUpdatedAt = json['mfaUpdatedAt'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['$id'] = id;
    data['$createdAt'] = createdAt;
    data['$updatedAt'] = updatedAt;
    data['userId'] = userId;
    data['expire'] = expire;
    data['provider'] = provider;
    data['providerUid'] = providerUid;
    data['providerAccessToken'] = providerAccessToken;
    data['providerAccessTokenExpiry'] = providerAccessTokenExpiry;
    data['providerRefreshToken'] = providerRefreshToken;
    data['ip'] = ip;
    data['osCode'] = osCode;
    data['osName'] = osName;
    data['osVersion'] = osVersion;
    data['clientType'] = clientType;
    data['clientCode'] = clientCode;
    data['clientName'] = clientName;
    data['clientVersion'] = clientVersion;
    data['clientEngine'] = clientEngine;
    data['clientEngineVersion'] = clientEngineVersion;
    data['deviceName'] = deviceName;
    data['deviceBrand'] = deviceBrand;
    data['deviceModel'] = deviceModel;
    data['countryCode'] = countryCode;
    data['countryName'] = countryName;
    data['current'] = current;
    data['factors'] = factors;
    data['secret'] = secret;
    data['mfaUpdatedAt'] = mfaUpdatedAt;
    return data;
  }
}
