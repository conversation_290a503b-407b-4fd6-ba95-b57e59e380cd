import 'package:appwrite/appwrite.dart';
import 'package:appwrite/models.dart' as appwrite_models;
import 'package:shadfdpcardgame/app/data/appwrite/models/create_account.dart';
import 'package:shadfdpcardgame/app/data/appwrite/models/get_account.dart';
import 'package:shadfdpcardgame/app/data/appwrite/models/get_session.dart';
import 'package:shadfdpcardgame/app/utils/custom_snack_bar.dart';
import 'package:get/get.dart';
import 'dart:convert';

class AppwriteService extends GetxService {
  late Client client;
  late Account account;
  late Databases databases;
  late Realtime realtimeTables;
  late Realtime realtimeUserCards;
  late Realtime realtimeUserDroppedCards;
  late Functions functions;
  var createUser = CreateAccountModel().obs;
  var getSession = GetUserSession().obs;
  var getUser = GetAccountModel().obs;

  @override
  void onInit() {
    super.onInit();
    _initializeClient();
    ever(getSession, _initialScreen);
  }

  void _initializeClient() {
    client = Client();
    client
        .setEndpoint('https://cloud.appwrite.io/v1')
        .setProject('6821100a00040b5db5a7')
        .setSelfSigned(
          status: false,
        ); // For self signed certificates, only use for development;

    account = Account(client);
    databases = Databases(client);
    realtimeTables = Realtime(client);
    realtimeUserCards = Realtime(client);
    realtimeUserDroppedCards = Realtime(client);
    functions = Functions(client);
  }

  void _initialScreen(GetUserSession? user) {
    if (user?.userId != null) {
      Get.offAllNamed('/navigator');
    } else {
      Get.offAllNamed('/login');
    }
  }

  Future<CreateAccountModel?> signUp(
    String name,
    String email,
    String password,
  ) async {
    try {
      final response = await account.create(
        userId: "unique()",
        name: name,
        email: email,
        password: password,
      );
      createUser.value = CreateAccountModel.fromJson(response.toMap());
      if (createUser.value.id != null) {
        await signIn(email, password);
      }
      return createUser.value;
    } catch (e) {
      Get.log(e.toString());
      customSnackBar("Failed!", e.toString(), "error");
      return null;
    }
  }

  Future<GetUserSession?> signIn(String email, String password) async {
    try {
      final response = await account.createEmailPasswordSession(
        email: email,
        password: password,
      );
      // print the response in a pretty format
      Get.log(jsonEncode(response.toMap()));
      final map = response.toMap();
      getSession.value = GetUserSession.fromJson(map);
      return getSession.value;
    } catch (e) {
      Get.log(e.toString());
      customSnackBar("Authentication Failed!", e.toString(), "error");
      return null;
    }
  }

  Future<void> logout() async {
    try {
      await account.deleteSession(sessionId: 'current');
      getSession.value = GetUserSession();
    } catch (e) {
      Get.log('Logout error: $e');
      throw Exception('Logout failed: $e');
    }
  }

  Future<bool> isAuthenticated() async {
    try {
      final response = await account.get();
      getUser.value = GetAccountModel.fromJson(response.toMap());
      Get.log("FROM IS AUTH GETUSER - ${jsonEncode(getUser.value.toJson())}");

      if (Get.currentRoute == "/splash" ||
          Get.currentRoute == "/login" ||
          Get.currentRoute == "/register") {
        Get.offAllNamed('/navigator');
      }
      return true;
    } catch (e) {
      Get.log(e.toString());
      if (Get.currentRoute != "/login" && Get.currentRoute != "/register") {
        Get.offAllNamed('/login');
      }
      return false;
    }
  }

  // Additional helper method for listing documents
  Future<appwrite_models.DocumentList> listDocuments({
    required String databaseId,
    required String collectionId,
  }) async {
    return await databases.listDocuments(
      databaseId: databaseId,
      collectionId: collectionId,
    );
  }
}
