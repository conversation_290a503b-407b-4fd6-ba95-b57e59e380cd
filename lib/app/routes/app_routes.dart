part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();
  static const REGISTER = _Paths.REGISTER;
  static const LOGIN = _Paths.LOGIN;
  static const SPLASH = _Paths.SPLASH;
  static const SETTINGS = _Paths.SETTINGS;
  static const NAVIGATOR = _Paths.NAVIGATOR;
  static const TABLES = _Paths.TABLES;
  static const GAME = _Paths.GAME;
}

abstract class _Paths {
  _Paths._();
  static const REGISTER = '/register';
  static const LOGIN = '/login';
  static const SPLASH = '/splash';
  static const SETTINGS = '/settings';
  static const NAVIGATOR = '/navigator';
  static const TABLES = '/tables';
  static const GAME = '/game';
}
