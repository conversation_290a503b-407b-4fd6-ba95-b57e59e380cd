import 'package:get/get.dart';

import '../modules/game/game_binding.dart';
import '../modules/game/game_view.dart';
import '../modules/login/login_binding.dart';
import '../modules/login/login_view.dart';
import '../modules/navigator/navigator_binding.dart';
import '../modules/navigator/navigator_view.dart';
import '../modules/register/register_binding.dart';
import '../modules/register/register_view.dart';
import '../modules/settings/settings_binding.dart';
import '../modules/settings/settings_controller.dart';
import '../modules/settings/settings_view.dart';
import '../modules/splash/splash_binding.dart';
import '../modules/splash/splash_view.dart';
import '../modules/tables/tables_binding.dart';
import '../modules/tables/tables_view.dart';

part 'app_routes.dart';

// put SettingsController here to be used in the entire app
final SettingsController settingsController = Get.put(SettingsController());

class AppPages {
  AppPages._();

  static const initial = Routes.SPLASH;

  static final routes = [
    GetPage(
      name: _Paths.REGISTER,
      page: () => RegisterView(),
      binding: RegisterBinding(),
    ),
    GetPage(
      name: _Paths.LOGIN,
      page: () => LoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: _Paths.SPLASH,
      page: () => SplashView(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: _Paths.SETTINGS,
      page: () => SettingsView(),
      binding: SettingsBinding(),
    ),
    GetPage(
      name: _Paths.NAVIGATOR,
      page: () => NavigatorView(),
      binding: NavigatorBinding(),
    ),
    GetPage(
      name: _Paths.TABLES,
      page: () => TablesView(),
      binding: TablesBinding(),
    ),
    GetPage(name: _Paths.GAME, page: () => GameView(), binding: GameBinding()),
  ];
}
