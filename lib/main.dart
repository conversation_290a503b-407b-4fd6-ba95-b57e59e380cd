import 'package:shadfdpcardgame/app/data/appwrite/appwrite_service.dart';
import 'package:shadfdpcardgame/app/modules/settings/settings_controller.dart';
import 'package:shadfdpcardgame/app/routes/app_pages.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart' as shadcn;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Get.putAsync(() async => AppwriteService());
  await Get.putAsync(() async => SettingsController());
  runApp(MainApp());
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    final SettingsController settingsController =
        Get.find<SettingsController>();
    return Obx(
      () => shadcn.ShadcnApp(
        theme: shadcn.ThemeData(
          colorScheme: settingsController.shadcnTheme(),
          radius: 0.5,
        ),
        home: GetMaterialApp(
          smartManagement: SmartManagement.full,
          debugShowCheckedModeBanner: false,
          themeMode: ThemeMode.system,
          initialRoute: AppPages.initial,
          getPages: AppPages.routes,
        ),
      ),
    );
  }
}
